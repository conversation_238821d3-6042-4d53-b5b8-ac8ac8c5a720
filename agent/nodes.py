"""Node implementations for the LangGraph agent."""

import time
from typing import Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import HumanMessage, SystemMessage

from config.settings import settings
from agent.state import AgentState


def summarize_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that summarizes the input text using OpenAI.
    
    Args:
        state: Current agent state
        
    Returns:
        Updated state with summary
    """
    start_time = time.time()
    
    try:
        # Initialize the OpenAI model
        llm = ChatOpenAI(
            model=settings.agent_model,
            temperature=settings.agent_temperature,
            max_tokens=settings.agent_max_tokens,
            openai_api_key=settings.openai_api_key
        )
        
        # Create the prompt for summarization
        system_message = SystemMessage(content="""
You are a helpful AI assistant that provides clear, concise summaries.
Your task is to summarize the given text in a way that captures the main points
and key information while being easy to understand.

Guidelines:
- Keep the summary concise but comprehensive
- Maintain the original meaning and context
- Use clear, simple language
- Highlight the most important points
""")
        
        human_message = HumanMessage(content=f"Please summarize the following text:\n\n{state['input']}")
        
        # Get the response from OpenAI
        response = llm.invoke([system_message, human_message])
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Update state with results
        return {
            "output": response.content,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "error": None
        }
        
    except Exception as e:
        processing_time = time.time() - start_time
        return {
            "output": None,
            "model_used": settings.agent_model,
            "processing_time": processing_time,
            "error": f"Error in summarization: {str(e)}"
        }


def validate_input_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that validates the input before processing.
    
    Args:
        state: Current agent state
        
    Returns:
        Updated state with validation results
    """
    input_text = state.get("input", "")
    
    # Basic validation
    if not input_text or not input_text.strip():
        return {
            "error": "Input text is empty or contains only whitespace"
        }
    
    # Check if input is too short to summarize meaningfully
    if len(input_text.strip()) < 10:
        return {
            "error": "Input text is too short to summarize (minimum 10 characters)"
        }
    
    # Check if input is extremely long (optional limit)
    if len(input_text) > 10000:
        return {
            "error": "Input text is too long (maximum 10,000 characters)"
        }
    
    # If validation passes, return empty dict (no state changes)
    return {}


def format_output_node(state: AgentState) -> Dict[str, Any]:
    """
    Node that formats the final output.
    
    Args:
        state: Current agent state
        
    Returns:
        Updated state with formatted output
    """
    if state.get("error"):
        # If there's an error, format it nicely
        return {
            "output": f"❌ Error: {state['error']}"
        }
    
    if not state.get("output"):
        return {
            "output": "❌ No summary generated"
        }
    
    # Format successful output with metadata
    formatted_output = f"""📝 **Summary:**

{state['output']}

---
🤖 **Model:** {state.get('model_used', 'Unknown')}
⏱️ **Processing Time:** {state.get('processing_time', 0):.2f}s
🕒 **Timestamp:** {state.get('timestamp', 'Unknown')}
"""
    
    return {
        "output": formatted_output
    }
