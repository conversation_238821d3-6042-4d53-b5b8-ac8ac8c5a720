"""Main LangGraph implementation for the summarization agent."""

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from agent.state import AgentState, create_initial_state
from agent.nodes import validate_input_node, summarize_node, format_output_node


def should_continue_after_validation(state: AgentState) -> str:
    """
    Conditional edge function to determine if processing should continue after validation.
    
    Args:
        state: Current agent state
        
    Returns:
        Next node name or END
    """
    if state.get("error"):
        return "format_output"
    return "summarize"


def create_agent_graph():
    """
    Create and configure the LangGraph agent.
    
    Returns:
        Compiled LangGraph agent
    """
    # Create the state graph
    workflow = StateGraph(AgentState)
    
    # Add nodes
    workflow.add_node("validate_input", validate_input_node)
    workflow.add_node("summarize", summarize_node)
    workflow.add_node("format_output", format_output_node)
    
    # Set entry point
    workflow.set_entry_point("validate_input")
    
    # Add conditional edge after validation
    workflow.add_conditional_edges(
        "validate_input",
        should_continue_after_validation,
        {
            "summarize": "summarize",
            "format_output": "format_output"
        }
    )
    
    # Add edge from summarize to format_output
    workflow.add_edge("summarize", "format_output")
    
    # Add edge from format_output to END
    workflow.add_edge("format_output", END)
    
    # Add memory for state persistence
    memory = MemorySaver()
    
    # Compile the graph
    app = workflow.compile(checkpointer=memory)
    
    return app


def run_agent(input_text: str, thread_id: str = "default") -> dict:
    """
    Run the agent with the given input.
    
    Args:
        input_text: Text to summarize
        thread_id: Thread ID for conversation tracking
        
    Returns:
        Agent response with summary and metadata
    """
    # Create the agent graph
    app = create_agent_graph()
    
    # Create initial state
    initial_state = create_initial_state(input_text)
    
    # Configure the thread
    config = {"configurable": {"thread_id": thread_id}}
    
    try:
        # Run the agent
        result = app.invoke(initial_state, config=config)
        
        return {
            "success": True,
            "output": result.get("output", "No output generated"),
            "metadata": {
                "model_used": result.get("model_used"),
                "processing_time": result.get("processing_time"),
                "timestamp": result.get("timestamp"),
                "thread_id": thread_id
            },
            "error": result.get("error")
        }
        
    except Exception as e:
        return {
            "success": False,
            "output": None,
            "metadata": {
                "thread_id": thread_id
            },
            "error": f"Agent execution failed: {str(e)}"
        }


# Example usage
if __name__ == "__main__":
    # Test the agent
    test_input = """
    Artificial Intelligence (AI) has become increasingly important in modern technology. 
    It encompasses machine learning, natural language processing, computer vision, and robotics. 
    AI systems can analyze large amounts of data, recognize patterns, and make decisions with minimal human intervention. 
    Applications include autonomous vehicles, medical diagnosis, financial trading, and virtual assistants. 
    While AI offers tremendous benefits, it also raises concerns about job displacement, privacy, and ethical considerations.
    """
    
    result = run_agent(test_input)
    print("Agent Result:")
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")
    print(f"Error: {result['error']}")
    print(f"Metadata: {result['metadata']}")
