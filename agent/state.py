"""State management for the LangG<PERSON>h agent."""

from typing import TypedDict, Optional, List, Dict, Any
from datetime import datetime


class AgentState(TypedDict):
    """State for the summarization agent."""
    
    # Input and output
    input: str
    output: Optional[str]
    
    # Processing metadata
    timestamp: Optional[str]
    model_used: Optional[str]
    processing_time: Optional[float]
    
    # Error handling
    error: Optional[str]
    
    # Additional context
    metadata: Optional[Dict[str, Any]]


def create_initial_state(input_text: str) -> AgentState:
    """Create initial state for the agent."""
    return AgentState(
        input=input_text,
        output=None,
        timestamp=datetime.now().isoformat(),
        model_used=None,
        processing_time=None,
        error=None,
        metadata={}
    )
