# LangGraph AI Agent

A Python-based AI agent built with LangGraph, featuring OpenAI integration, LangSmith monitoring, and a Flask admin panel.

## Features

- **LangGraph Agent**: Core AI agent with summarization capabilities
- **OpenAI Integration**: Uses OpenAI models for text processing
- **LangSmith Monitoring**: Real-time agent monitoring and tracing
- **Flask Admin Panel**: Web interface for testing and monitoring
- **Modular Architecture**: Clean, extensible codebase

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd <your-repo-name>

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# - Add your OpenAI API key
# - Add your LangSmith API key (optional)
```

### 3. Run the Application

```bash
# Start the Flask admin panel
python web/app.py
```

Visit `http://localhost:5000` to access the admin panel.

## Project Structure

```
├── agent/              # Core agent implementation
│   ├── graph.py       # Main LangGraph definition
│   ├── nodes.py       # Graph node implementations
│   └── state.py       # Graph state management
├── config/            # Configuration management
│   └── settings.py    # Settings and environment handling
├── web/               # Flask admin panel
│   ├── app.py         # Flask application
│   ├── templates/     # HTML templates
│   └── static/        # CSS and JS files
├── tests/             # Unit tests
└── requirements.txt   # Python dependencies
```

## Usage

### Using the Agent Directly

```python
from agent.graph import create_agent_graph

# Create and run the agent
graph = create_agent_graph()
result = graph.invoke({"input": "Summarize the benefits of renewable energy"})
print(result["output"])
```

### Using the Admin Panel

1. Navigate to `http://localhost:5000`
2. Enter your prompt in the text area
3. Click "Send to Agent"
4. View the response and execution details

## Configuration

Key environment variables:

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `LANGCHAIN_API_KEY`: Your LangSmith API key (optional)
- `AGENT_MODEL`: OpenAI model to use (default: gpt-3.5-turbo)
- `AGENT_TEMPERATURE`: Model temperature (default: 0.7)

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black .
flake8 .
```

## License

MIT License
